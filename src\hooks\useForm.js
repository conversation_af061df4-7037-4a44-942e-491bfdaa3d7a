import { deepCopy, isBoolean } from '@vueuse/core'
import { reactive, ref } from 'vue'

export function useForm(initFormData = {}, initFormRules = {}) {
  const rowData = deepCopy(initFormData)

  const form = ref({ ...initFormData })
  const rules = reactive({ ...initFormRules })
  const formRef = ref(null)

  const validateForm = async (fields) => {
    if (!formRef.value) {
      console.warn('formRef is not assigned yet')
      return false
    }
    try {
      const valid = await formRef.value.validate(fields)
      return isBoolean(valid) ? valid : false
    } catch (error) {
      return false
    }
  }

  const resetForm = () => {
    resetData()
    if (formRef.value) {
      formRef.value.resetFields()
    }
  }

  const resetData = () => {
    const rowKeys = Object.keys(rowData)
    const currentForm = form.value

    Object.keys(currentForm).forEach((key) => {
      if (rowKeys.includes(key)) {
        currentForm[key] = deepCopy(rowData[key])
      } else {
        delete currentForm[key]
      }
    })

    rowKeys.forEach((key) => {
      if (!(key in currentForm)) {
        currentForm[key] = deepCopy(rowData[key])
      }
    })
  }

  const clearValidate = () => {
    if (formRef.value) {
      formRef.value.clearValidate()
    }
  }

  return {
    form,
    rules,
    formRef,
    validateForm,
    resetForm,
    clearValidate,
    resetData,
  }
}
